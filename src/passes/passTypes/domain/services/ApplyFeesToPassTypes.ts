import {
  EFeeType,
  right,
} from '@discocil/fv-domain-library/domain';
import { EValueType, GetOptionPricingService } from '@discocil/fv-pricing-library/ticketing';

import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type { Fees } from '@/fees/domain/entities/Fee';
import type { PassTypes } from '@/passes/passTypes/domain/entities/PassType';
import type {
  Either,
  InvalidArgumentError,
  MoneyError,
  NotFoundError,
  UnexpectedError,
} from '@discocil/fv-domain-library/domain';
import type { AdminFeeConfigPrimitives } from '@discocil/fv-pricing-library/ticketing';

type ApplyFeesToPassTypesInput = {
  readonly customerCustomFees: Fees;
  readonly passTypes: PassTypes;
};

type ApplyFeesToPassTypesOutput = Either<InvalidArgumentError | NotFoundError | MapperError | UnexpectedError | MoneyError, PassTypes>;

export class ApplyFeesToPassTypes {
  execute(input: ApplyFeesToPassTypesInput): ApplyFeesToPassTypesOutput {
    const { customerCustomFees, passTypes } = input;

    const customerCustomFeesPrimitives = customerCustomFees.toArray();

    for (const passType of passTypes.values()) {
      const priceOrEmpty = passType.getPrice();

      if (priceOrEmpty.isEmpty()) {
        continue;
      }

      const price = priceOrEmpty.get();

      const adminFeeConfig: AdminFeeConfigPrimitives = {
        type: price.serviceFees.type === EFeeType.PERCENTAGE ? EValueType.PERCENTAGE : EValueType.FIXED,
        value: price.serviceFees.quantity,
      };

      const passPricing = GetOptionPricingService.execute({
        price: price.price,
        currency: price.currency,
        customerCustomFees: customerCustomFeesPrimitives,
        adminFeeConfig,
      });

      price.setPrice(passPricing.totalAmount);
    }

    return right(passTypes);
  }
}
