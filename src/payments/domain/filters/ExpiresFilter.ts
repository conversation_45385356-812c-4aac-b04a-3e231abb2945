import {
  Filter,
  FilterField as FilterFieldBase,
  FilterOperator,
  FilterValue,
} from '@discocil/fv-criteria-converter-library/domain';
import { FvDate } from '@discocil/fv-domain-library/domain';

import type { PaymentKeys } from '../entities/Payment';

class FilterField extends FilterFieldBase<PaymentKeys> {}

export class ExpiresFilter {
  private static readonly field: PaymentKeys = 'expiresAt';

  static buildActive(): Filter {
    const field = new FilterField(this.field);
    const operator = FilterOperator.greaterEqualThan();
    const filterValue = FilterValue.build(FvDate.create().toMilliseconds());

    return new Filter(field, operator, filterValue);
  }
}
