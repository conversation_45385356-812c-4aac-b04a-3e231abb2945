import {
  contextualizeError,
  Either2,
  NotFoundError,
  type UseCase,
} from '@discocil/fv-domain-library';

import { EventRepository } from '@/events/events/domain/contracts/EventRepository';
import { EventEntity } from '@/events/events/domain/entities/EventEntity';
import { EventCriteriaMother } from '@/events/events/domain/filters/EventCriteriaMother';

import { PaymentRepository } from '../domain/contracts/PaymentRepository';
import { PaginatedPaymentsEither } from '../domain/entities/Payment';
import { PaymentCriteriaMother } from '../domain/filters/PaymentCriteriaMother';

import type { SearchPaymentDto } from '../domain/contracts/SearchPaymentsDtoContract';

export class SearchPaymentsUseCase implements UseCase<SearchPaymentDto, Promise<PaginatedPaymentsEither>> {
  constructor(
    private readonly eventRepository: EventRepository,
    private readonly paymentRepository: PaymentRepository,
  ) { }

  @contextualizeError()
  async execute(dto: SearchPaymentDto): Promise<PaginatedPaymentsEither> {
    const {
      pagination,
      eventId,
      idx,
      status,
    } = dto;

    const eventCriteria = EventCriteriaMother.idToMatch(dto.eventId);

    const eventOrError = await this.eventRepository.find(eventCriteria);

    if (eventOrError.isLeft()) {
      return Either2.left(eventOrError.value);
    }

    const event = eventOrError.value;

    if (!event.getIsActive()) {
      return Either2.left(NotFoundError.build({
        context: this.constructor.name,
        target: EventEntity.name,
        data: { eventId },
      }));
    }

    const paymentCriteria = PaymentCriteriaMother.searchPayments(eventId, idx, status, pagination);

    const paymentsWithPaginationorError = await this.paymentRepository.search(paymentCriteria);

    if (paymentsWithPaginationorError.isLeft()) {
      return Either2.left(paymentsWithPaginationorError.get());
    }

    const paymentsWithPagination = paymentsWithPaginationorError.get();

    return Either2.right({ payments: paymentsWithPagination });
  }
}
