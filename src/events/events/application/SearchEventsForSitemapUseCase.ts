import {
  left,
  right,
  UniqueEntityID,
  contextualizeError,
} from '@discocil/fv-domain-library/domain';

import { BillingAddressCriteriaMother } from '@/billingAddresses/domain/filters/BillingAddressCriteriaMother';
import { LogSoftError } from '@/cross-cutting/domain/errors/LogSoftError';
import { EventCriteriaMother } from '@/events/events/domain/filters/EventCriteriaMother';
import { OrganizationCriteriaMother } from '@/organizations/organizations/domain/filters/OrganizationCriteriaMother';

import type { BillingAddressRepository } from '@/billingAddresses/domain/contracts/BillingAddressRepository';
import type { BillingAddress } from '@/billingAddresses/domain/entities/BillingAddress';
import type { Artist } from '@/events/artists/domain/entities/Artist';
import type { EventRepository } from '@/events/events/domain/contracts/EventRepository';
import type { SearchEventByMicrositeDto } from '@/microsite/domain/contracts/SearchEventDtoContract';
import type { OrganizationRepository } from '@/organizations/organizations/domain/contracts/OrganizationRepository';
import type { UseCase } from '@discocil/fv-domain-library/application';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { SearchEventEither } from '../domain/entities/EventEntity';

export class SearchEventsForSitemapUseCase implements UseCase<SearchEventByMicrositeDto, Promise<SearchEventEither>> {
  constructor(
    private readonly eventRepository: EventRepository,
    private readonly billingAddressRepository: BillingAddressRepository,
    private readonly organizationRepository: OrganizationRepository,
  ) {}

  @contextualizeError()
  async execute(dto: SearchEventByMicrositeDto): Promise<SearchEventEither> {
    const { pagination } = dto;

    const organizationCriteria = OrganizationCriteriaMother.availableByMicrositeToMatch();
    const organizationsResult = await this.organizationRepository.search(organizationCriteria);

    if (organizationsResult.isLeft()) {
      return left(organizationsResult.value);
    }

    const organizationIds = new Set<string>();
    const artistsUniqueIds = new Set<string>();

    for (const [id] of organizationsResult.value) {
      organizationIds.add(id);
    }

    const eventCriteria = EventCriteriaMother.availableByMicrositeToMatch(organizationIds, pagination);
    const eventResult = await this.eventRepository.search(eventCriteria);

    if (eventResult.isLeft()) {
      return left(eventResult.value);
    }

    organizationIds.clear();
    const eventsWithPagination = eventResult.value;


    for (const event of eventsWithPagination.events.values()) {
      organizationIds.add(event.organizationId);

      event.artists.forEach(id => artistsUniqueIds.add(id));
    }

    const organizationUniqueIds = [...organizationIds].map((id: string) => UniqueEntityID.build(id));
    const billingAddressCriteria = BillingAddressCriteriaMother.organizationsToMatch(organizationUniqueIds);

    const billingAddressesResult = await this.billingAddressRepository.search(billingAddressCriteria);

    const organizations = organizationsResult.value;
    const billingAddresses = billingAddressesResult.isRight() ? billingAddressesResult.value : new Map<IdPrimitive, BillingAddress>();

    for (const [id, event] of eventsWithPagination.events) {
      const hasBillingAddress = billingAddresses.has(event.organizationId);

      if (!hasBillingAddress) {
        this.logSoft(event.organizationId);

        eventsWithPagination.events.delete(id);
      }
    }

    return right({
      ...eventsWithPagination,
      organizations,
      billingAddresses,
      artists: new Map<IdPrimitive, Artist>(),
    });
  }

  private logSoft(organizationId: IdPrimitive): void {
    LogSoftError.billingAddressNotFound(organizationId);
  }
}
