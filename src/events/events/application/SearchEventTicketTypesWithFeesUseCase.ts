import {
  contextualizeError,
  ECustomFeeApplyType,
  left,
  right,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';


import type { SearchFeesUseCase } from '@/fees/application/SearchFeesUseCase';
import type { ShouldShowFeesUpfrontService } from '@/organizations/organizationConfigurations/domain/services/ShouldShowFeesUpfront';
import type { ApplyFeesToTicketTypes } from '@/tickets/ticketsTypes/domain/services/ApplyFeesToTicketTypes';
import type { UseCase } from '@discocil/fv-domain-library/application';
import type { SearchEventTicketsTypesEither } from '../domain/contracts/ticketType/EventTicketTypeTypes';
import type { SearchEventTicketTypesDto } from '../domain/contracts/ticketType/SearchEventTicketTypesContract';
import type { SearchEventTicketsTypesUseCase } from './SearchEventTicketsTypesUseCase';

export class SearchEventTicketTypesWithFeesUseCase implements UseCase<SearchEventTicketTypesDto, Promise<SearchEventTicketsTypesEither>> {
  constructor(
    private readonly searchEventTicketsTypesUseCase: SearchEventTicketsTypesUseCase,
    private readonly shouldShowFeesUpfrontService: ShouldShowFeesUpfrontService,
    private readonly searchFeesUseCase: SearchFeesUseCase,
    private readonly applyFeesToTicketTypes: ApplyFeesToTicketTypes,
  ) {}

  @contextualizeError()
  async execute(dto: SearchEventTicketTypesDto): Promise<SearchEventTicketsTypesEither> {
    const searchEventTicketsTypesResult = await this.searchEventTicketsTypesUseCase.execute(dto);

    if (searchEventTicketsTypesResult.isLeft()) {
      return left(searchEventTicketsTypesResult.value);
    }

    const {
      event, ticketTypes, pagination,
    } = searchEventTicketsTypesResult.value;

    const organizationId = UniqueEntityID.build(event.organizationId);

    const shouldShowFeesUpfront = await this.shouldShowFeesUpfrontService.execute(organizationId);

    if (!shouldShowFeesUpfront) {
      return right({
        event,
        ticketTypes,
        pagination,
        language: dto.language,
      });
    }

    const searchFeesResult = await this.searchFeesUseCase.execute({
      applyTo: ECustomFeeApplyType.TICKETS,
      organizationId,
    });

    if (searchFeesResult.isLeft()) {
      return left(searchFeesResult.value);
    }

    const applyFeesResult = this.applyFeesToTicketTypes.execute({
      customerCustomFees: searchFeesResult.value,
      ticketTypes,
    });

    if (applyFeesResult.isLeft()) {
      return left(applyFeesResult.value);
    }

    const ticketTypesWithFees = applyFeesResult.value;

    return right({
      event,
      ticketTypes: ticketTypesWithFees,
      pagination,
      language: dto.language,
    });
  }
}
