import { ValidatorComposite } from '@discocil/fv-domain-library/application';
import {
  EMicrositeChannel,
  left,
  NotFoundError,
  right,
  UniqueEntityID,
  contextualizeError,
} from '@discocil/fv-domain-library/domain';

import { BillingAddressCriteriaMother } from '@/billingAddresses/domain/filters/BillingAddressCriteriaMother';
import { BlockCriteriaMother } from '@/blocks/domain/filters/BlockCriteriaMother';
import { LogSoftError } from '@/cross-cutting/domain/errors/LogSoftError';
import { ArtistCriteriaMother } from '@/events/artists/domain/filters/ArtistCriteriaMother';
import { OrganizationCriteriaMother } from '@/organizations/organizations/domain/filters/OrganizationCriteriaMother';

import { EventGroup } from '../domain/entities/EventGroup';
import { IsBlockedFor } from '../domain/services/IsBlockedFor';

import { SearchEventsDatesValidation } from './validations/SearchEventsDatesValidation';
import { SearchEventsSlugValidation } from './validations/SearchEventsSlugValidation';

import type { BillingAddressRepository } from '@/billingAddresses/domain/contracts/BillingAddressRepository';
import type { BillingAddress } from '@/billingAddresses/domain/entities/BillingAddress';
import type { BlockRepository } from '@/blocks/domain/contracts/BlockRepository';
import type { ArtistRepository } from '@/events/artists/domain/contracts/ArtistRepository';
import type {
  Artist,
  Artists,
  ArtistsEither,
} from '@/events/artists/domain/entities/Artist';
import type { LocationByEventsSearcher } from '@/locations/domain/services/LocationByEventsSearcher';
import type { IMicrositeChannelService } from '@/microsite/domain/contracts/GetMicrositeChannelContracts';
import type { SearchEventDto } from '@/microsite/domain/contracts/SearchEventDtoContract';
import type { OrganizationRepository } from '@/organizations/organizations/domain/contracts/OrganizationRepository';
import type { Organization } from '@/organizations/organizations/domain/entities/Organization';
import type { User } from '@/user/domain/entities/User';
import type { UseCase } from '@discocil/fv-domain-library/application';
import type { IdPrimitive } from '@discocil/fv-domain-library/domain';
import type { SearchEventEither } from './../domain/entities/EventEntity';
import type { EventForMicrositeFactory } from './../domain/services/EventForMicrositeFactory';

export class SearchEventsUseCase implements UseCase<SearchEventDto, Promise<SearchEventEither>> {
  constructor(
    private readonly micrositeChannelService: IMicrositeChannelService,
    private readonly billingAddressRepository: BillingAddressRepository,
    private readonly organizationRepository: OrganizationRepository,
    private readonly artistRepository: ArtistRepository,
    private readonly blockRepository: BlockRepository,
    private readonly locationByEventsSearcher: LocationByEventsSearcher,
    private readonly eventForMicrositeFactory: EventForMicrositeFactory,
  ) {}

  @contextualizeError()
  async execute(dto: SearchEventDto): Promise<SearchEventEither> {
    const {
      slug,
      organizationSlugs,
      eventGroupCodes,
      endDate,
      startDate,
      pagination,
      isPrivate,
    } = dto;

    const validator = ValidatorComposite.build();

    validator.add(SearchEventsDatesValidation.build());
    validator.add(SearchEventsSlugValidation.build());

    const validations = validator.execute({
      endDate,
      startDate,
      slug,
      organizationSlugs,
      context: this.constructor.name,
    });

    if (validations.isLeft()) {
      return left(validations.value);
    }

    const micrositeChannelOrError = await this.micrositeChannelService.execute({
      slug: slug.get(),
      organizationSlugs,
      eventGroupCodes,
    });

    if (micrositeChannelOrError.isLeft()) {
      return left(micrositeChannelOrError.value);
    }

    const micrositeChannel = micrositeChannelOrError.value;

    const eventGroupCodesAreFilled = eventGroupCodes.size > 0;
    const foundEventGroupsAreEmpty = micrositeChannel.eventGroups.size === 0;

    if (eventGroupCodesAreFilled && foundEventGroupsAreEmpty) {
      return left(NotFoundError.build({
        context: this.constructor.name,
        target: EventGroup.name,
      }).notAutoContextualizable());
    }

    const eventService = this.eventForMicrositeFactory.execute(micrositeChannel.type, eventGroupCodes);

    const eventResult = await eventService.execute({
      micrositeChannel,
      organizationSlugs,
      eventGroupCodes,
      startDate: startDate.get(),
      endDate: endDate.get(),
      pagination,
      isPrivate,
    });

    if (eventResult.isLeft()) {
      return left(eventResult.value);
    }

    const eventsWithPagination = eventResult.value;
    const events = eventsWithPagination.events;

    const organizationUniqueIds = new Set<string>();
    const artistsUniqueSpotifyIds = new Set<string>();
    const eventIds: UniqueEntityID[] = [];

    events.forEach((event) => {
      const {
        id, organizationId, artists,
      } = event;

      eventIds.push(UniqueEntityID.build(id));
      organizationUniqueIds.add(organizationId);

      artists.forEach(spotifyId => artistsUniqueSpotifyIds.add(spotifyId));
    });

    const organizationsUids = [...organizationUniqueIds].map(id => UniqueEntityID.build(id));

    const billingAddressCriteria = BillingAddressCriteriaMother.organizationsToMatch(organizationsUids);
    const organizationCriteria = OrganizationCriteriaMother.idsToMatch(organizationsUids);
    const blockCriteria = BlockCriteriaMother.eventsBlockedToMatch(eventIds);

    const [billingAddressesResult, organizationsResult, artistsResult, blocksResult, locationResult] = await Promise.all([
      this.billingAddressRepository.search(billingAddressCriteria),
      this.organizationRepository.search(organizationCriteria),
      this.searchArtists(artistsUniqueSpotifyIds),
      this.blockRepository.search(blockCriteria),
      this.locationByEventsSearcher.execute(events),
    ]);

    if (organizationsResult.isLeft()) {
      return left(organizationsResult.value);
    }

    const organizations = organizationsResult.value;
    const billingAddresses = billingAddressesResult.isRight() ? billingAddressesResult.value : new Map<IdPrimitive, BillingAddress>();

    if (artistsResult.isLeft()) {
      return left(artistsResult.value);
    }

    const eventsOrganization = organizationsResult.value;
    const blocks = blocksResult.isLeft() ? [] : blocksResult.value;

    for (const event of events.values()) {
      const hasBillingAddress = billingAddresses.has(event.organizationId);

      if (!hasBillingAddress) {
        this.logSoft(event.organizationId);

        events.delete(event.id);

        continue;
      }

      blocks.forEach((_block) => {
        if (_block.hasThisEvent(event.id)) {
          event.addBlockedOrganization(_block.organizationAssignedId);
        }
      });

      const eventOrganization = eventsOrganization.get(event.organizationId);

      if (eventOrganization) {
        event.setOrganization(eventOrganization);
      }

      const { type, channel } = micrositeChannel;
      const blockedOrganizations = this.getBlockedOrganizations(type, channel);
      const isBlockedFor = new IsBlockedFor(event, blockedOrganizations);
      const hasBlock = isBlockedFor.execute();

      if (hasBlock) {
        events.delete(event.id);
      }

      const location = locationResult.get(event.id);

      if (location) {
        event.setLocation(location);
      }
    }

    const artists: Artists = new Map<IdPrimitive, Artist>();

    for (const artist of artistsResult.value.values()) {
      const spotifyId = artist.spotify.id;

      if (spotifyId.isDefined()) {
        artists.set(spotifyId.get(), artist);
      }
    }

    return right({
      ...eventsWithPagination,
      organizations,
      billingAddresses,
      artists,
    });
  }

  private getBlockedOrganizations(type: EMicrositeChannel, channel: unknown): Organization | string[] {
    if (type === EMicrositeChannel.ORGANIZATION) {
      return channel as Organization;
    }

    const user = channel as User;

    return user.organizations;
  }

  private async searchArtists(artists: Set<string>): Promise<ArtistsEither> {
    if (artists.size === 0) {
      return right(new Map<IdPrimitive, Artist>());
    }

    const criteria = ArtistCriteriaMother.spotifyIdsToMatch([...artists.values()]);

    return this.artistRepository.search(criteria);
  }

  private logSoft(organizationId: IdPrimitive): void {
    LogSoftError.billingAddressNotFound(organizationId);
  }
}
