import { Component } from '@discocil/fv-domain-library/application';
import { left, right } from '@discocil/fv-domain-library/domain';

import { EventDatesIncompatible } from '../../domain/errors/EventDatesIncompatible';
import { SearchEventErrors } from '../../domain/errors/SearchEventErrors';

import type { SearchEventDto } from '@/microsite/domain/contracts/SearchEventDtoContract';
import type { Either } from '@discocil/fv-domain-library/domain';

type Request = Pick<SearchEventDto, 'startDate' | 'endDate'> & {
  context: string;
};

export class SearchEventsDatesValidation extends Component {
  private static instance: SearchEventsDatesValidation;

  static build(): SearchEventsDatesValidation {
    if (!SearchEventsDatesValidation.instance) {
      SearchEventsDatesValidation.instance = new SearchEventsDatesValidation();
    }

    return SearchEventsDatesValidation.instance;
  }

  execute(request: Request): Either<EventDatesIncompatible, boolean> {
    const { startDate, endDate } = request;

    if (startDate.isEmpty() || endDate.isEmpty()) {
      return left(SearchEventErrors.dateRangeIsRequired({ context: this.constructor.name }));
    }

    const _startDate = startDate.get();
    const _endDate = endDate.get();

    if (_startDate.isGreaterThanYear(_endDate.value)) {
      return left(EventDatesIncompatible.dateRangeInvalid({
        context: this.constructor.name,
        data: {
          startDate: _startDate.toISO(),
          endDate: _endDate.toISO(),
        },
      }));
    }

    if (_endDate.isLessThan(_startDate.value)) {
      return left(EventDatesIncompatible.startDateInvalid({
        context: this.constructor.name,
        data: {
          startDate: _startDate.toISO(),
          endDate: _endDate.toISO(),
        },
      }));
    }

    return right(true);
  }
}
