import { Component } from '@discocil/fv-domain-library/application';
import { left, right } from '@discocil/fv-domain-library/domain';

import { SearchEventErrors } from '../../domain/errors/SearchEventErrors';

import type { SearchEventDto } from '@/microsite/domain/contracts/SearchEventDtoContract';
import type { Either } from '@discocil/fv-domain-library/domain';
import type { EventDatesIncompatible } from '../../domain/errors/EventDatesIncompatible';

type Request = Pick<SearchEventDto, 'slug'> & {
  context: string;
};

export class SearchEventsSlugValidation extends Component {
  private static instance: SearchEventsSlugValidation;

  static build(): SearchEventsSlugValidation {
    if (!SearchEventsSlugValidation.instance) {
      SearchEventsSlugValidation.instance = new SearchEventsSlugValidation();
    }

    return SearchEventsSlugValidation.instance;
  }

  execute(request: Request): Either<EventDatesIncompatible, boolean> {
    const { slug } = request;

    if (slug.isEmpty()) {
      return left(SearchEventErrors.slugIsRequired({ context: this.constructor.name }));
    }

    return right(true);
  }
}
