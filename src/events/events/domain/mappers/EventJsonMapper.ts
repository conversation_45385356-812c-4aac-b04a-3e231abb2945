import {
  FvDate,
  left,
  Maybe,
  right,
} from '@discocil/fv-domain-library/domain';

import { entityStampsFrom<PERSON>son, entityStampsToJson } from '@/cross-cutting/domain/services/Stamps';
import { EventEntity } from '@/events/events/domain/entities/EventEntity';
import { EventPreregister } from '@/events/events/domain/entities/EventPreregister';

import { BookingSpaceAvailability } from '../entities/BookingSpaceAvailability';

import type {
  Images,
  ImagesExternalPrimitives,
  ImageSizes,
} from '@/cross-cutting/domain/contracts/Images';
import type { EventEither, EventPrimitivesBuild } from '@/events/events/domain/entities/EventEntity';
import type { EventPreregisterExternalPrimitives, EventPreregisterPrimitives } from '@/events/events/domain/entities/EventPreregister';
import type { DatePrimitive, Nullable } from '@discocil/fv-domain-library/domain';
import type { BookingSpaceAvailabilityExternalPrimitives } from '../entities/BookingSpaceAvailability';

export type EventJsonPrimitives = Omit<EventPrimitivesBuild,
  'canceled'
  | 'description'
  | 'image'
  | 'images'
  | 'imagePlan'
  | 'province'
  | 'provinceSlug'
  | 'qrMenuId'
  | 'tzName'
  | 'parentalAuthorization'
  | 'preregister'
  | 'date'
  | 'startDate'
  | 'endDate'
  | 'dateLimitSale'
  | 'createdAt'
  | 'updatedAt'
  | 'removedAt'
  | 'bookingSpaceAvailability'
> & Nullable<{
  canceled: number;
  description: string;
  image: string;
  images: ImagesExternalPrimitives;
  imagePlan: string;
  province: string;
  provinceSlug: string;
  qrMenuId: string;
  tzName: string;
  parentalAuthorization: string;
  preregister: EventPreregisterExternalPrimitives;
  bookingSpaceAvailability: BookingSpaceAvailabilityExternalPrimitives;
}> & {
  date: string;
  startDate: string;
  endDate: string;
  dateLimitSale: string;
  createdAt: string;
  updatedAt: string;
  removedAt: string;
};

export class EventJsonMapper {
  static toEntity(primitives: EventJsonPrimitives): EventEither {
    const isoToDate = (value: string): FvDate =>
      FvDate.createFromISO(value).value as FvDate;

    let eventPreregister = Maybe.none<EventPreregisterPrimitives>();

    if (primitives.preregister) {
      const preregister = primitives.preregister;

      const eventPreregisterOrError = EventPreregister.build({
        id: preregister.id,
        isActive: preregister.isActive,
        endDate: preregister.endDate
          ? Maybe.some(FvDate.create(preregister.endDate).toPrimitive())
          : Maybe.none<DatePrimitive>(),
      });

      if (eventPreregisterOrError.isRight()) {
        eventPreregister = Maybe.fromValue(eventPreregisterOrError.value);
      }
    }

    let eventBookingSpaceAvailability = Maybe.none<BookingSpaceAvailability>();

    if (primitives.bookingSpaceAvailability) {
      const bookingSpaceAvailability = primitives.bookingSpaceAvailability;

      const bookingSpaceAvailabilityResultOrError = BookingSpaceAvailability.build({
        id: bookingSpaceAvailability.id,
        token: bookingSpaceAvailability.token,
        expiredAt: bookingSpaceAvailability.expiredAt,
        eventId: bookingSpaceAvailability.eventId,
        collaboratorId: bookingSpaceAvailability.collaboratorId,
      });

      if (bookingSpaceAvailabilityResultOrError.isRight()) {
        eventBookingSpaceAvailability = Maybe.fromValue(bookingSpaceAvailabilityResultOrError.value);
      }
    }

    const date = isoToDate(primitives.date);
    const startDate = isoToDate(primitives.startDate);
    const endDate = isoToDate(primitives.endDate);
    const dateLimitSale = isoToDate(primitives.dateLimitSale);

    const eventOrError = EventEntity.build({
      ...primitives,
      canceled: primitives.canceled
        ? Maybe.fromValue(FvDate.createFromSeconds(primitives.canceled).toPrimitive())
        : Maybe.none<Date>(),
      description: Maybe.fromValue(primitives.description),
      image: Maybe.fromValue(primitives.image),
      images: this.imageBuild(primitives.images),
      imagePlan: Maybe.fromValue(primitives.imagePlan),
      province: Maybe.fromValue(primitives.province),
      provinceSlug: Maybe.fromValue(primitives.provinceSlug),
      qrMenuId: Maybe.fromValue(primitives.qrMenuId),
      tzName: Maybe.fromValue(primitives.tzName),
      parentalAuthorization: Maybe.fromValue(primitives.parentalAuthorization),
      preregister: eventPreregister,
      date: date.toPrimitive(),
      startDate: startDate.toPrimitive(),
      endDate: endDate.toPrimitive(),
      dateLimitSale: dateLimitSale.toPrimitive(),
      ...entityStampsFromJson(primitives),
    });

    if (eventOrError.isLeft()) {
      return left(eventOrError.value);
    }

    if (eventBookingSpaceAvailability.isDefined()) {
      eventOrError.value.setBookingSpaceAvailability(eventBookingSpaceAvailability.get());
    }

    return right(eventOrError.value);
  }

  static toJson(event: EventEntity): EventJsonPrimitives {
    return {
      id: event.id,
      organizationId: event.organizationId,
      isActive: event.isActive,
      atmosphere: event.atmosphere,
      applyCommissionEvent: event.applyCommissionEvent,
      artists: event.artists,
      canceled: event.getCanceledDateInSeconds().fold(() => null, item => item),
      code: event.code,
      currency: event.currency,
      demo: event.demo,
      description: event.description.fold(() => null, item => item),
      day: event.day,
      age: event.age,
      date: event.getDateInISO(),
      dateLimitSale: event.getDateLimitSaleInISO(),
      photosNum: event.photosNum,
      functions: event.functions,
      musicalGenres: event.musicalGenres,
      image: event.image.fold(() => null, item => item),
      images: event.images.fold(() => null, item => ({
        medium: item.medium.fold(() => null, item => item),
        mini: item.mini.fold(() => null, item => item),
        small: item.small.fold(() => null, item => item),
      })),
      imagePlan: event.imagePlan.fold(() => null, item => item),
      municipality: event.municipality,
      businessImage: event.businessImage,
      businessName: event.businessName,
      name: event.name,
      perch: event.perch,
      plan: event.plan,
      processed: event.processed,
      processing: event.processing,
      province: event.province.fold(() => null, item => item),
      provinceSlug: event.provinceSlug.fold(() => null, item => item),
      qrMenuId: event.qrMenuId.fold(() => null, item => item),
      services: event.services,
      showImagePlan: event.showImagePlan,
      showLocation: event.showLocation,
      slug: event.slug,
      smsPrice: event.smsPrice,
      smsSent: event.smsSent,
      tzName: event.tzName.fold(() => null, item => item),
      locationId: event.locationId,
      variableDiscocil: event.variableDiscocil,
      isVisible: event.isVisible,
      changeNamePrice: event.changeNamePrice,
      startDate: event.getStartDateInISO(),
      endDate: event.getEndDateInISO(),
      parentalAuthorization: event.parentalAuthorization.fold(() => null, item => item),
      preregister: event.getPreregisterPrimitives().fold(() => null, item => item),
      isPrivate: event.isPrivate,
      bookingSpaceAvailability: event.getBookingSpaceAvailabilityPrimitives().fold(() => null, item => item),
      ...entityStampsToJson(event),
      isUntilLate: event.isUntilLate,
    };
  }

  private static imageBuild(imageIncoming: ImagesExternalPrimitives | null | undefined): Maybe<Images> {
    if (imageIncoming === null || imageIncoming === undefined) {
      return Maybe.none<Images>();
    }

    const image = imageIncoming;

    return Maybe.some(
      {
        medium: this.imageSizesBuild(image.medium),
        mini: this.imageSizesBuild(image.mini),
        small: this.imageSizesBuild(image.small),
      },
    );
  }

  private static imageSizesBuild(image: ImageSizes | null): Maybe<ImageSizes> {
    if (image === null) {
      return Maybe.none<ImageSizes>();
    }

    return Maybe.some({
      original: image.original,
      versioned: image.versioned,
      sized: image.sized,
      isDefault: image.isDefault,
    });
  }
}
