import mongoose from 'mongoose';

export const eventconfigurationSchema = {
  _id: {
    type: String,
    required: true,
  },
  evento_id: {
    type: String,
    required: true,
    index: true,
  },
  clave: {
    type: String,
    required: true,
    index: true,
  },
  valor: mongoose.Schema.Types.Mixed,
  ticket_cancellation_time: {
    type: Number,
    default: -1,
  },
  created_at: { type: Number },
  created_by: { type: String },
  updated_at: { type: Number },
  updated_by: { type: String },
  removed_at: {
    type: Number,
    index: true,
  },
  removed_by: { type: String },
};
