import {
  Maybe, type IdPrimitive, type Nullable,
} from '@discocil/fv-domain-library/domain';

import { entityStampsFromJson, entityStampsToJson } from '@/cross-cutting/domain/services/Stamps';

import { GuestListLimit } from '../entities/GuestListLimit';

import type { GuestListLimitEither, GuestListLimitPrimitives } from '../entities/GuestListLimit';

export type GuestListLimitsJsonPrimitives = Omit<GuestListLimitPrimitives,
  'organizationId'
  | 'userId'
  | 'createdAt'
  | 'updatedAt'
  | 'removedAt'
> & Nullable<{
  readonly organizationId: IdPrimitive;
  readonly userId: IdPrimitive;
}> & {
  createdAt: string;
  updatedAt: string;
  removedAt: string;
};

export class GuestListLimitsJsonMapper {
  static toEntity(primitives: GuestListLimitsJsonPrimitives): GuestListLimitEither {
    return GuestListLimit.build({
      ...primitives,
      organizationId: Maybe.fromValue(primitives.organizationId),
      userId: Maybe.fromValue(primitives.userId),
      ...entityStampsFromJson(primitives),
    });
  }

  static toJson(guestList: GuestListLimit): GuestListLimitsJsonPrimitives {
    return {
      ...guestList.toPrimitives(),
      organizationId: guestList.organizationId.fold(() => null, item => item),
      userId: guestList.userId.fold(() => null, item => item),
      ...entityStampsToJson(guestList),
    };
  }
}
