import { entityStampsFrom<PERSON>son, entityStampsToJson } from '@/cross-cutting/domain/services/Stamps';

import { GuestList } from '../entities/GuestList';

import type { GuestListEither, GuestListPrimitives } from '../entities/GuestList';

export type GuestListJsonPrimitives = Omit<GuestListPrimitives,
  'createdAt'
  | 'updatedAt'
  | 'removedAt'
> & {
  createdAt: string;
  updatedAt: string;
  removedAt: string;
};

export class GuestListJsonMapper {
  static toEntity(primitives: GuestListJsonPrimitives): GuestListEither {
    return GuestList.build({
      ...primitives,
      ...entityStampsFromJson(primitives),
    });
  }

  static toJson(guestList: GuestList): GuestListJsonPrimitives {
    return {
      ...guestList.toPrimitives(),
      ...entityStampsTo<PERSON>son(guestList),
    };
  }
}
