import {
  left,
  Maybe,
  Money,
  right,
  UnexpectedError,
} from '@discocil/fv-domain-library/domain';
import { GetTicketsPricingService } from '@discocil/fv-pricing-library/ticketing';

import { TicketType } from '@/tickets/ticketsTypes/domain/entities/TicketTypeEntity';

import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type { Fees } from '@/fees/domain/entities/Fee';
import type { TicketTypeEither, TicketTypes } from '@/tickets/ticketsTypes/domain/entities/TicketTypeEntity';
import type {
  Either,
  IdPrimitive,
  InvalidArgumentError,
  MoneyError,
  NotFoundError,
} from '@discocil/fv-domain-library/domain';
import type { InputTicketPrimitives, TicketPricingResponse } from '@discocil/fv-pricing-library/ticketing';

type ApplyFeesToTicketTypesInput = {
  readonly customerCustomFees: Fees;
  readonly ticketTypes: TicketTypes;
};

type ApplyFeesToTicketTypesOutput = Either<InvalidArgumentError | NotFoundError | MapperError | UnexpectedError | MoneyError, TicketTypes>;

export class ApplyFeesToTicketTypes {
  execute(input: ApplyFeesToTicketTypesInput): ApplyFeesToTicketTypesOutput {
    const { customerCustomFees, ticketTypes } = input;

    const customerCustomFeesPrimitives = customerCustomFees.toArray();

    const ticketTypesWithFees = new Map<IdPrimitive, TicketType>();

    for (const ticketTypeOriginal of ticketTypes.values()) {
      const { currency, id: ticketTypeId } = ticketTypeOriginal;

      const inputTickets: InputTicketPrimitives[] = ticketTypeOriginal.getOptions().map(option => ({
        ticketTypeId,
        optionId: option.id,
        unitPrice: option.price,
        addOns: [],
        isWarrantySelected: false,
        adminFeeConfig: option.getAdminConfig(),
      }));

      const ticketsPricing = GetTicketsPricingService.execute({
        tickets: inputTickets,
        currency,
        customerCustomFees: customerCustomFeesPrimitives,
      });

      const ticketTypeWithFees = this.applyFeesToTicketTypeOptions(ticketTypeOriginal, ticketsPricing);

      if (ticketTypeWithFees.isLeft()) {
        return left(ticketTypeWithFees.value);
      }

      ticketTypesWithFees.set(ticketTypeId, ticketTypeWithFees.value);
    }

    return right(ticketTypesWithFees);
  }

  private applyFeesToTicketTypeOptions(
    ticketTypeOriginal: TicketType,
    ticketsPricing: TicketPricingResponse[],
  ): TicketTypeEither {
    const ticketTypeCopy = TicketType.build(ticketTypeOriginal.toPrimitives());

    if (ticketTypeCopy.isLeft()) {
      return left(ticketTypeCopy.value);
    }

    const ticketType = ticketTypeCopy.value;

    for (const option of ticketType.getOptions()) {
      const optionPriceWithFees = Maybe.fromValue(ticketsPricing.find(ticketPricing => ticketPricing.optionId === option.id)?.totalAmount);

      if (optionPriceWithFees.isEmpty()) {
        return left(UnexpectedError.build({
          context: this.constructor.name,
          data: {
            options: ticketType.getOptions(),
            ticketsPricing,
            optionId: option.id,
          },
          target: 'ticketPricing',
        }));
      }

      const optionPriceWithFeesMoneyResult = Money.build({
        amount: optionPriceWithFees.get(),
        currency: ticketType.currency,
      });

      if (optionPriceWithFeesMoneyResult.isLeft()) {
        return left(optionPriceWithFeesMoneyResult.value);
      }

      option.setPrice(optionPriceWithFeesMoneyResult.value);
    }

    return right(ticketType);
  }
}
