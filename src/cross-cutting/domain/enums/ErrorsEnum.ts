export const enum EErrorKeys {
  ENTITY_MAPPER = 'entity_mapper',
  ENTITY_NOT_FOUND = 'entity_not_found',
  EXTENSION_NOT_ALLOWED = 'extension_not_allowed',
  INCOMPATIBLE_FIELDS = 'incompatible_fields',
  INTERNAL_ERROR = 'internal_error',
  INVALID_FIELD = 'invalid_field',
  INVALID_ORGANIZATION_HOST = 'invalid_organization_host',
  INVALID_TICKET_TYPE_PACK_SIZE = 'invalid_ticket_type_pack_size',
  INVALID_USER = 'invalid_user',
  MISSING_CREDENTIALS = 'missing_credentials',
  PAYMENT_GATEWAY_BAD_CONFIGURATION = 'payment_gateway_bad_configuration',
  PRICING_NOT_AVAILABLE = 'pricing_not_available',
  RATE_NOT_ALLOWED_TO_SELL = 'rate_not_allowed_to_sell',
  RATE_NOT_EXIST_FOR_EVENT = 'rate_not_exist_for_event',
  RATE_NOT_EXIST_FOR_ORGANIZATION = 'rate_not_exist_for_organization',
  REQUIRED_FILTERS = 'required_filters',
  TICKET_ACTIVATION_NOT_POSSIBLE = 'ticket_activation_not_possible',
  UNEXPECTED_ERROR = 'unexpected_error',
  UNSUPPORTED_EVENT = 'unsupported_event',
  WITHOUT_PERSONAL_LIMITS = 'without_personal_limits',
  BOOKING_SPACE_AVAILABILITY_NOT_FOUND = 'booking_space_availability_not_found',
  BOOKING_SPACE_AVAILABILITY_EXPIRED = 'booking_space_availability_expired',
  PAYLINK_ALREADY_USED = 'paylink_already_used'
}
