import { EGGDD, Money } from '@discocil/fv-domain-library/domain';

import type { ECurrency } from '@discocil/fv-domain-library/domain';

export const ggddBusinessCommission = (ggddType: EGGDD, ggddAmount: number, price: Money, currency: ECurrency): Money => {
  if (ggddType === EGGDD.FIX) {
    return Money.build({ amount: ggddAmount, currency }).value as Money;
  }

  return price.percentage(ggddAmount);
};
