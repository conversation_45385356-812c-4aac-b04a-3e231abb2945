import { EErrorKeys } from '@/cross-cutting/domain/enums/ErrorsEnum';
import { HTTP_CODES } from '@app/http/HttpCodes';

import type { FvError } from '@discocil/fv-domain-library/domain';
import type { FastifyError } from 'fastify';

type ErrorInfo = {
  readonly code: number;
  readonly message: string;
};

const getCode = (error: FvError): number => {
  const errorCause = error.cause;

  return errorCause in matchCode ? matchCode[errorCause as EErrorKeys] : HTTP_CODES.INTERNAL_SERVER_ERROR_500;
};

export const buildMessage = (error: FvError | FastifyError): string => {
  return error.message;
};

const matchCode: Record<EErrorKeys, HTTP_CODES> = {
  [EErrorKeys.ENTITY_MAPPER]: HTTP_CODES.UNPROCESSABLE_ENTITY_422,
  [EErrorKeys.ENTITY_NOT_FOUND]: HTTP_CODES.NOT_FOUND_404,
  [EErrorKeys.EXTENSION_NOT_ALLOWED]: HTTP_CODES.CONFLICT_409,
  [EErrorKeys.INCOMPATIBLE_FIELDS]: HTTP_CODES.BAD_REQUEST_400,
  [EErrorKeys.INTERNAL_ERROR]: HTTP_CODES.INTERNAL_SERVER_ERROR_500,
  [EErrorKeys.INVALID_FIELD]: HTTP_CODES.CONFLICT_409,
  [EErrorKeys.INVALID_ORGANIZATION_HOST]: HTTP_CODES.CONFLICT_409,
  [EErrorKeys.INVALID_TICKET_TYPE_PACK_SIZE]: HTTP_CODES.CONFLICT_409,
  [EErrorKeys.INVALID_USER]: HTTP_CODES.UNAUTHORIZED_401,
  [EErrorKeys.MISSING_CREDENTIALS]: HTTP_CODES.CONFLICT_409,
  [EErrorKeys.PAYMENT_GATEWAY_BAD_CONFIGURATION]: HTTP_CODES.PAYMENT_REQUIRED_402,
  [EErrorKeys.PRICING_NOT_AVAILABLE]: HTTP_CODES.CONFLICT_409,
  [EErrorKeys.RATE_NOT_ALLOWED_TO_SELL]: HTTP_CODES.CONFLICT_409,
  [EErrorKeys.RATE_NOT_EXIST_FOR_EVENT]: HTTP_CODES.CONFLICT_409,
  [EErrorKeys.RATE_NOT_EXIST_FOR_ORGANIZATION]: HTTP_CODES.CONFLICT_409,
  [EErrorKeys.REQUIRED_FILTERS]: HTTP_CODES.BAD_REQUEST_400,
  [EErrorKeys.TICKET_ACTIVATION_NOT_POSSIBLE]: HTTP_CODES.CONFLICT_409,
  [EErrorKeys.UNEXPECTED_ERROR]: HTTP_CODES.INTERNAL_SERVER_ERROR_500,
  [EErrorKeys.UNSUPPORTED_EVENT]: HTTP_CODES.CONFLICT_409,
  [EErrorKeys.WITHOUT_PERSONAL_LIMITS]: HTTP_CODES.CONFLICT_409,
  [EErrorKeys.BOOKING_SPACE_AVAILABILITY_EXPIRED]: HTTP_CODES.CONFLICT_409,
  [EErrorKeys.BOOKING_SPACE_AVAILABILITY_NOT_FOUND]: HTTP_CODES.CONFLICT_409,
  [EErrorKeys.PAYLINK_ALREADY_USED]: HTTP_CODES.NOT_FOUND_404,
};

// eslint-disable-next-line no-restricted-syntax
export const getErrorInfo = (error: FvError): ErrorInfo => {
  return {
    code: getCode(error),
    message: buildMessage(error),
  };
};
