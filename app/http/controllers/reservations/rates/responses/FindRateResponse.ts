import { HTTP_CODES } from '@app/http/HttpCodes';
import {
  defaultAmountDetails, type AmountDetails, type AmountDetailsPrimitives,
} from '@/cross-cutting/domain/contracts/AmountDetails';

import type { Maybe } from '@discocil/fv-domain-library/domain';
import type { Rate } from '@/reservations/rates/domain/entities/Rate';
import type { successResponseSchema } from '@app/http/@types/cli-api/reservations/rates/find/successResponseSchema';
import type { IResponse } from '@app/http/responses/ResponseContracts';
import type { Static } from '@sinclair/typebox';

type Response = Static<typeof successResponseSchema>;

export const FindRateResponse = (): IResponse<Rate, Response> => {
  const execute = (rate: Rate): Response => {
    const pendingFullPayment = buildAmountDetails(rate.getFullPaymentAmount());
    const pendingDepositPayment = buildAmountDetails(rate.getDepositPaymentAmount());

    const response = {
      rateId: rate.id,
      price: rate.amount.amount,
      currency: rate.amount.currency,
      allowCompletePayment: rate.isFullPaymentAllowed,
      pendingFullPayment,
      pendingDepositPayment,
      color: rate.color,
      includes: rate.includes.fold(() => null, includes => includes),
      conditions: rate.conditions.fold(() => null, conditions => conditions),
    };

    return response;
  };

  const buildAmountDetails = (amount: Maybe<AmountDetails>): AmountDetailsPrimitives => {
    return amount.fold(() => defaultAmountDetails, amount => ({
      baseAmount: amount.baseAmount.toDecimal(),
      feeAmount: amount.feeAmount.toDecimal(),
      totalAmount: amount.totalAmount.toDecimal(),
    }));
  };

  return {
    execute,
    status: (): HTTP_CODES => HTTP_CODES.OK_200,
  };
};
