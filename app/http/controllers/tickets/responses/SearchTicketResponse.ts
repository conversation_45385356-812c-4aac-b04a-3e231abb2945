import { HTTP_CODES } from '@app/http/HttpCodes';
import { paginationResponse } from '@app/http/responses/PaginationResponse';

import { buildCustomer, urlsTicketResponse } from './FindTicketResponse';

import type { EventEntity } from '@/events/events/domain/entities/EventEntity';
import type { SearchTicketsUseCaseResponse } from '@/tickets/tickets/domain/contracts/SearchTicketsUseCaseContracts';
import type { Ticket } from '@/tickets/tickets/domain/entities/TicketEntity';
import type { TicketType } from '@/tickets/ticketsTypes/domain/entities/TicketTypeEntity';
import type { successResponseSchema } from '@app/http/@types/cli-api/tickets/search/successResponseSchema';
import type { IResponse } from '@app/http/responses/ResponseContracts';
import type { Static } from '@sinclair/typebox';

type Response = Static<typeof successResponseSchema>;

type TicketPayment = {
  id: string;
  totalImport: number;
  feesTotal: number;
  currency: string;
};

export const SearchTicketResponse = (): IResponse<SearchTicketsUseCaseResponse, Response> => {
  const execute = (dto: SearchTicketsUseCaseResponse): Response['data'] => {
    const {
      tickets, ticketTypes, payments, events,
    } = dto;

    return [...tickets.values()].map((ticket: Ticket) => {
      const event = events.get(ticket.eventId) as EventEntity;
      const ticketType = ticketTypes.get(ticket.typeId) as TicketType;
      let ticketPayment: TicketPayment | null = null;

      if (ticket.paymentId.isDefined()) {
        const payment = payments.get(ticket.paymentId.get());

        if (payment) {
          ticketPayment = {
            id: payment.id,
            totalImport: payment.totalImport,
            feesTotal: payment.feesTotal,
            currency: payment.currency,
          };
        }
      }

      return {
        id: ticket.id,
        organizationId: ticket.organizationId,
        purchaseId: ticket.purchaseId.fold(() => null, item => item),
        type: {
          id: ticket.typeId,
          name: ticketType.name,
          hideCountdown: ticketType.hasHideCountdown,
        },
        event: {
          id: ticket.eventId,
          name: event.name,
          date: event.getDateInMilliseconds(),
        },
        payment: ticketPayment,
        option: {
          price: ticket.option.price,
          name: ticket.option.name.fold(() => null, value => value),
          discountCode: ticket.discountCode.fold(() => null, item => item.code),
          discountAmountApplied: ticket.discountAmountApplied,
        },
        code: ticket.code,
        customer: buildCustomer(ticket),
        state: ticket.state,
        nominative: ticket.nominative,
        remarketing: ticket.remarketing,
        urls: urlsTicketResponse(ticket),
        warrantyTotal: ticket.getWarrantyTotal().toDecimal(),
        supplements: ticket.supplements.map((_supplement) => {
          return {
            label: _supplement.label,
            purchaseQuantity: _supplement.purchaseQuantity.fold(() => null, item => item.quantity),
            price: _supplement.price,
          };
        }),
      };
    });
  };

  return {
    execute: paginationResponse(execute),
    status: (): HTTP_CODES => HTTP_CODES.OK_200,
  };
};
