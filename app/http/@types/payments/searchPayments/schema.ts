import { EPaymentStates } from '@discocil/fv-domain-library';
import { Type } from '@sinclair/typebox';

import { paginationSchema } from '../../cli-api/cross-cutting/PaginationSchemas';

import { successResponseSchema } from './successResponseSchema';

import type { FastifyReplyTypebox, FastifyRequestTypebox } from '../../cli-api/cross-cutting/schema';

const paramsSchema = Type.Object({});

const querySchema = Type.Object({
  eventId: Type.String(),
  idx: Type.String(),
  status: Type.Literal(EPaymentStates.PENDING),
});

const querySchemaWithPagination = Type.Intersect([querySchema, paginationSchema]);

const errorResponseSchema = Type.Object({ message: Type.String() });

const responseSchema = {
  '2xx': successResponseSchema,
  '4xx': errorResponseSchema,
  '5xx': errorResponseSchema,
};

export const searchPaymentsSchema = {
  params: paramsSchema,
  querystring: querySchemaWithPagination,
  response: responseSchema,
};

type Schema = typeof searchPaymentsSchema;

export type SearchPaymentsRequest = FastifyRequestTypebox<Schema>;

export type SearchPaymentsReply = FastifyReplyTypebox<Schema>;
