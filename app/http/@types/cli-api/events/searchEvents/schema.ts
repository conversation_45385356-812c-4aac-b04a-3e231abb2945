import { Type } from '@sinclair/typebox';


import { querySearchEventsSchema } from './querySchema';
import { successResponseSchema } from './successResponseSchema';

import type { FastifyReplyTypebox, FastifyRequestTypebox } from '../../cross-cutting/schema';

export const paramsSchema = Type.Object({});

const errorResponseSchema = Type.Object({ message: Type.String() });

const responseSchema = {
  '2xx': successResponseSchema,
  '4xx': errorResponseSchema,
  '5xx': errorResponseSchema,
};

export const searchEventSchema = {
  params: paramsSchema,
  querystring: querySearchEventsSchema,
  response: responseSchema,
};

type Schema = typeof searchEventSchema;

export type SearchEventsRequest = FastifyRequestTypebox<Schema>;
export type SearchEventsReply = FastifyReplyTypebox<Schema>;
