import { Type } from '@sinclair/typebox';

import { paginationMetadata } from '../../cross-cutting/PaginationSchemas';
import { Nullable } from '../../cross-cutting/schema';

const organizationSchema = {
  id: Type.String(),
  image: Nullable(Type.String()),
  cover: Nullable(Type.String()),
  slug: Nullable(Type.String()),
};

const organizerSchema = {
  name: Type.String(),
  cif: Nullable(Type.String()),
  address: Type.String(),
  postalCode: Type.String(),
  city: Nullable(Type.String()),
  province: Type.String(),
};

const artistsSchema = {
  name: Type.String(),
  image: Type.String(),
};

const locationSchema = {
  timezone: Type.Object({
    id: Type.String(),
    name: Type.String(),
    dstOffset: Nullable(Type.Number()),
    rawOffset: Nullable(Type.Number()),
  }),
  addressComplete: Nullable(Type.String()),
};

export const successResponseSchema = Type.Object({
  data: Type.Array(
    Type.Object({
      id: Type.String(),
      organization: Type.Object(organizationSchema),
      genres: Type.Array(Type.String()),
      atmosphere: Type.Array(Type.String()),
      age: Type.Number(),
      organizer: Nullable(Type.Object(organizerSchema)),
      name: Type.String(),
      description: Nullable(Type.String()),
      dates: Type.Object({
        date: Type.Number(),
        start: Type.Number(),
        end: Type.Number(),
        canceled: Nullable(Type.Number()),
      }),
      slug: Type.String(),
      code: Type.String(),
      image: Nullable(Type.String()),
      artists: Type.Array(Type.Object(artistsSchema)),
      location: Nullable(Type.Object(locationSchema)),
      updatedAt: Type.Number(),
      isUntilLate: Type.Boolean(),
    }),
  ),
  metadata: paginationMetadata,
});
