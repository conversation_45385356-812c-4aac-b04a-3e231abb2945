import { EGGDD } from '@discocil/fv-domain-library/domain';
import { Type } from '@sinclair/typebox';

import { Nullable } from '../../cross-cutting/schema';

const options = {
  id: Type.String(),
  price: Type.Number(),
  name: Nullable(Type.String()),
  ggddType: Type.Enum(EGGDD),
  ggddAmount: Type.Number(),
  age: Type.Number(),
  content: Nullable(Type.String()),
  additionalInfo: Nullable(Type.String()),
  to: Type.Number(),
  max: Type.Number(),
  image: Nullable(Type.String()),
};

const questions = {
  id: Type.String(),
  label: Type.String(),
  type: Type.String(),
  items: Type.Array(Type.String()),
  required: Type.Boolean(),
  deletedAt: Type.Number(),
};

const supplements = {
  id: Type.String(),
  label: Type.String(),
  description: Nullable(Type.String()),
  price: Type.Number(),
  fakePrice: Nullable(Type.Number()),
  purchaseLimit: Nullable(Type.Object({
    minQuantity: Type.Number(),
    maxQuantity: Nullable(Type.Number()),
    isUnlimited: Type.Boolean(),
  })),
  redemptionDeadline: Nullable(Type.Number()),
  order: Nullable(Type.Number()),
};

const warranty = {
  enabled: Type.Boolean(),
  percentage: Type.Number(),
  hours: Type.Number(),
  isFixed: Type.Boolean(),
  fixedPrice: Nullable(Type.Number()),
};

export const customersSchema = {
  max: Type.Number(),
  min: Type.Number(),
  quantity: Type.Number(),
};

export const successResponseSchema = Type.Object({
  id: Type.String(),
  name: Type.String(),
  organizationId: Type.String(),
  eventId: Type.String(),
  slug: Type.String(),
  type: Type.String(),
  summary: Nullable(Type.String()),
  options: Type.Array(Type.Object(options)),
  questions: Type.Array(Type.Object(questions)),
  supplements: Type.Array(Type.Object(supplements)),
  fields: Nullable(Type.Record(Type.String(), Type.Unknown())),
  warranty: Type.Object(warranty),
  customers: Type.Object(customersSchema),
  nominative: Type.Boolean(),
});
