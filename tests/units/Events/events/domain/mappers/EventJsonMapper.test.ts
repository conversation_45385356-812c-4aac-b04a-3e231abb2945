import { entityStampsToJson } from '@/cross-cutting/domain/services/Stamps';
import { EventJsonMapper } from '@/events/events/domain/mappers/EventJsonMapper';
import { EventMother } from '@tests/stubs/event/EventMother';

describe(`${EventJsonMapper.name}`, () => {
  const event = EventMother.buildDefault();

  it('should serialize and deserialize a event', () => {
    const json = EventJsonMapper.toJson(event);

    expect(json.id).toBe(event.id);
    expect(json.organizationId).toBe(event.organizationId);
    expect(json.isActive).toBe(event.isActive);
    expect(json.atmosphere).toEqual(event.atmosphere);
    expect(json.applyCommissionEvent).toBe(event.applyCommissionEvent);
    expect(json.artists).toEqual(event.artists);
    expect(json.canceled).toBe(event.canceled.fold(() => null, item => item));
    expect(json.code).toBe(event.code);
    expect(json.currency).toBe(event.currency);
    expect(json.demo).toBe(event.demo);
    expect(json.description).toBe(event.description.fold(() => null, item => item));
    expect(json.day).toBe(event.day);
    expect(json.age).toBe(event.age);
    expect(json.date).toBe(event.getDateInISO());
    expect(json.dateLimitSale).toBe(event.getDateLimitSaleInISO());
    expect(json.photosNum).toBe(event.photosNum);
    expect(json.functions).toEqual(event.functions);
    expect(json.musicalGenres).toEqual(event.musicalGenres);
    expect(json.image).toBe(event.image.fold(() => null, item => item));
    expect(json.images).toStrictEqual(event.images.fold(() => null, (item) => {
      return {
        medium: item.medium.fold(() => null, item => item),
        mini: item.mini.fold(() => null, item => item),
        small: item.small.fold(() => null, item => item),
      };
    }));
    expect(json.imagePlan).toBe(event.imagePlan.fold(() => null, item => item));
    expect(json.municipality).toBe(event.municipality);
    expect(json.businessImage).toBe(event.businessImage);
    expect(json.businessName).toBe(event.businessName);
    expect(json.name).toBe(event.name);
    expect(json.perch).toBe(event.perch);
    expect(json.plan).toBe(event.plan);
    expect(json.processed).toBe(event.processed);
    expect(json.processing).toBe(event.processing);
    expect(json.province).toBe(event.province.fold(() => null, item => item));
    expect(json.provinceSlug).toBe(event.provinceSlug.fold(() => null, item => item));
    expect(json.qrMenuId).toBe(event.qrMenuId.fold(() => null, item => item));
    expect(json.services).toStrictEqual(event.services);
    expect(json.showImagePlan).toBe(event.showImagePlan);
    expect(json.showLocation).toBe(event.showLocation);
    expect(json.slug).toBe(event.slug);
    expect(json.smsPrice).toBe(event.smsPrice);
    expect(json.smsSent).toBe(event.smsSent);
    expect(json.tzName).toBe(event.tzName.fold(() => null, item => item));
    expect(json.locationId).toBe(event.locationId);
    expect(json.variableDiscocil).toBe(event.variableDiscocil);
    expect(json.isVisible).toBe(event.isVisible);
    expect(json.changeNamePrice).toBe(event.changeNamePrice);
    expect(json.startDate).toBe(event.getStartDateInISO());
    expect(json.endDate).toBe(event.getEndDateInISO());
    expect(json.parentalAuthorization).toBe(event.parentalAuthorization.fold(() => null, item => item));
    expect(json.preregister).toStrictEqual(event.getPreregisterPrimitives().fold(() => null, item => item));
    expect(json.isPrivate).toBe(event.isPrivate);
    expect(json.isUntilLate).toBe(event.isUntilLate);

    const stamps = entityStampsToJson(event);

    expect(json.createdAt).toBe(stamps.createdAt);
    expect(json.createdBy).toBe(stamps.createdBy);
    expect(json.updatedAt).toBe(stamps.updatedAt);
    expect(json.updatedBy).toBe(stamps.updatedBy);
    expect(json.removedAt).toBe(stamps.removedAt);
    expect(json.removedBy).toBe(stamps.removedBy);

    const entityOrError = EventJsonMapper.toEntity(json);

    expect(entityOrError.isRight()).toBeTruthy();
  });
});
