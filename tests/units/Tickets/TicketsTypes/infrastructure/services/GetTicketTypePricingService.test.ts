import {
  left,
  right,
} from '@discocil/fv-domain-library/domain';
import { mock } from 'jest-mock-extended';

import { GetTicketTypePricingService } from '@/tickets/ticketsTypes/infrastructure/services/GetTicketTypePricingService';
import { OptionPriceError } from '@/tickets/ticketsTypes/domain/errors/OptionPriceError';
import { EventMother } from '@tests/stubs/event/EventMother';
import { TicketTypeMother } from '@tests/stubs/ticketType/TicketTypeMother';
import { TicketTypeOptionMother } from '@tests/stubs/ticketType/TicketTypeOptionMother';

import type { TicketTypeOption } from '@/tickets/ticketsTypes/domain/entities/TicketTypeOption';
import type { IGetOptionsService } from '@/tickets/tickets/domain/contracts/GetOptionsContracts';
import type { GetTicketTypePricingRequest, TicketTypePricing } from '@/tickets/ticketsTypes/domain/contracts/PricingTicketContract';

describe(`${GetTicketTypePricingService.name}`, () => {
  const getOptionsService = mock<IGetOptionsService>();
  const service = new GetTicketTypePricingService(getOptionsService);
  const ticketType = TicketTypeMother.createPublicPassingAllValidation();

  const request: GetTicketTypePricingRequest = {
    ticketType,
    event: EventMother.buildDefault(),
    amount: 5,
  };

  afterEach(async () => {
    jest.clearAllMocks();
  });

  it('should error', async () => {
    const optionPriceError = OptionPriceError.couldNotGetThePrice({
      context: 'GetOptionsService',
      target: request.amount,
    });

    getOptionsService.execute.mockResolvedValue(left(optionPriceError));

    const serviceResult = await service.execute(request);

    expect(serviceResult.isLeft()).toBeTruthy();
    expect(serviceResult.isRight()).toBeFalsy();

    expect(serviceResult.value).toBeInstanceOf(OptionPriceError);

    const errorResponse = serviceResult.value as OptionPriceError;

    expect(errorResponse.message).toStrictEqual(optionPriceError.message);
    expect(errorResponse.cause).toStrictEqual(optionPriceError.cause);
  });

  it('should return a success', async () => {
    const optionsRequest = TicketTypeOptionMother.getMany(5, ticketType.currency);

    getOptionsService.execute.mockResolvedValue(right(optionsRequest));

    const serviceResult = await service.execute(request);

    expect(serviceResult.isLeft()).toBeFalsy();
    expect(serviceResult.isRight()).toBeTruthy();

    const ticketTypePricings = serviceResult.value as TicketTypePricing;

    const ids = new Set<string>();

    optionsRequest.forEach((option) => {
      ids.add(option.id);
      expect(ticketTypePricings.prices.get(option.id)?.toDecimal()).toStrictEqual(option.price);
    });

    const samePrice = ids.size === 1;

    ticketTypePricings.options.forEach((option) => {
      const optionRequest = optionsRequest.find(item => item.id === option.id) as TicketTypeOption;

      expect(option.id).toStrictEqual(optionRequest.id);
      expect(option.price).toStrictEqual(optionRequest.price);
      expect(option.ggddType).toStrictEqual(optionRequest.ggddType);
      expect(option.ggddAmount).toStrictEqual(optionRequest.ggddAmount);
    });

    expect(ticketTypePricings.prices).toBeInstanceOf(Map);
    expect(ticketTypePricings.fees).toBeInstanceOf(Map);
    expect(ticketTypePricings.warranties).toBeInstanceOf(Map);

    expect(ticketTypePricings.ids).toStrictEqual(ids);
    expect(ticketTypePricings.samePrice).toStrictEqual(samePrice);

    expect(Array.isArray(ticketTypePricings.options)).toStrictEqual(true);
  });
});
